# Google API Key Management for MCP Rules Engine
# Creates API keys with restrictions and proper labeling per tenant

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable required APIs
resource "google_project_service" "apikeys" {
  service = "apikeys.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "serviceusage" {
  service = "serviceusage.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

# Create API keys for each tenant
resource "google_apikeys_key" "mcp_api_keys" {
  for_each = toset(var.tenants)
  
  name         = "mcp-rules-api-${each.value}"
  display_name = "MCP Rules Engine API Key - ${each.value}"
  
  restrictions {
    api_targets {
      service = "rules-api"  # This should match your API Gateway service name
      methods = [
        "GET /health",
        "POST /api/v1/deadlines/calculate"
      ]
    }
    
    # Optional: Add IP restrictions if needed
    # server_key_restrictions {
    #   allowed_ips = ["0.0.0.0/0"]  # Restrict to specific IPs in production
    # }
  }

  labels = {
    tenant      = each.value
    service     = "mcp-rules-engine"
    environment = terraform.workspace
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.apikeys,
    google_project_service.serviceusage
  ]
}

# Store the API keys in Secret Manager
resource "google_secret_manager_secret_version" "mcp_api_key_versions" {
  for_each = toset(var.tenants)
  
  secret      = google_secret_manager_secret.mcp_api_keys[each.value].id
  secret_data = google_apikeys_key.mcp_api_keys[each.value].key_string
  
  depends_on = [google_apikeys_key.mcp_api_keys]
  
  lifecycle {
    # Prevent accidental destruction of API keys
    prevent_destroy = true
  }
}

# IAM for API key management (for rotation script)
resource "google_project_iam_custom_role" "mcp_key_manager" {
  role_id     = "mcpKeyManager"
  title       = "MCP API Key Manager"
  description = "Custom role for managing MCP Rules Engine API keys"
  
  permissions = [
    "apikeys.keys.create",
    "apikeys.keys.delete",
    "apikeys.keys.get",
    "apikeys.keys.list",
    "apikeys.keys.update",
    "secretmanager.versions.add",
    "secretmanager.versions.access",
    "secretmanager.secrets.get"
  ]
}

# Service account for key rotation
resource "google_service_account" "mcp_key_rotator" {
  account_id   = "mcp-key-rotator"
  display_name = "MCP API Key Rotation Service Account"
  description  = "Service account used by rotation scripts to manage MCP API keys"
}

# Bind the custom role to the rotation service account
resource "google_project_iam_member" "mcp_key_rotator_binding" {
  project = var.project_id
  role    = google_project_iam_custom_role.mcp_key_manager.name
  member  = "serviceAccount:${google_service_account.mcp_key_rotator.email}"
}

# Output API key information (without exposing the actual keys)
output "mcp_api_key_names" {
  description = "Names of created MCP API keys"
  value = {
    for tenant in var.tenants : tenant => google_apikeys_key.mcp_api_keys[tenant].name
  }
}

output "mcp_key_rotator_email" {
  description = "Email of the key rotation service account"
  value       = google_service_account.mcp_key_rotator.email
}

# Create service account key for the rotation script
resource "google_service_account_key" "mcp_key_rotator_key" {
  service_account_id = google_service_account.mcp_key_rotator.name
  public_key_type    = "TYPE_X509_PEM_FILE"
}

# Store the service account key in Secret Manager
resource "google_secret_manager_secret" "rotator_service_account_key" {
  secret_id = "mcp-key-rotator-sa-key"
  
  labels = {
    service     = "mcp-rules-engine"
    purpose     = "key-rotation"
    environment = terraform.workspace
    managed_by  = "terraform"
  }

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "rotator_sa_key_version" {
  secret      = google_secret_manager_secret.rotator_service_account_key.id
  secret_data = base64decode(google_service_account_key.mcp_key_rotator_key.private_key)
}
