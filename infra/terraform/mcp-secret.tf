# MCP Rules Engine Secret Manager Configuration
# Creates per-tenant secrets for MCP API keys

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Variables for tenant configuration
variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "tenants" {
  description = "List of tenant IDs to create secrets for"
  type        = list(string)
  default     = ["pilot-smith", "demo-tenant"]
}

variable "region" {
  description = "Google Cloud region"
  type        = string
  default     = "us-central1"
}

# Create Secret Manager secrets for each tenant
resource "google_secret_manager_secret" "mcp_api_keys" {
  for_each = toset(var.tenants)
  
  secret_id = "mcp-key-${each.value}"
  
  labels = {
    tenant      = each.value
    service     = "mcp-rules-engine"
    environment = terraform.workspace
    managed_by  = "terraform"
  }

  replication {
    auto {}
  }

  depends_on = [google_project_service.secretmanager]
}

# Enable Secret Manager API
resource "google_project_service" "secretmanager" {
  service = "secretmanager.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

# IAM binding for Core AiLex service account to access secrets
resource "google_secret_manager_secret_iam_binding" "mcp_secret_accessor" {
  for_each = toset(var.tenants)
  
  secret_id = google_secret_manager_secret.mcp_api_keys[each.value].secret_id
  role      = "roles/secretmanager.secretAccessor"
  
  members = [
    "serviceAccount:core-ailex@${var.project_id}.iam.gserviceaccount.com",
    "serviceAccount:${var.project_id}@appspot.gserviceaccount.com", # App Engine default SA
  ]
}

# Output the secret names for reference
output "mcp_secret_names" {
  description = "Names of created MCP API key secrets"
  value = {
    for tenant in var.tenants : tenant => google_secret_manager_secret.mcp_api_keys[tenant].name
  }
}

output "mcp_secret_ids" {
  description = "Full resource names of MCP API key secrets"
  value = {
    for tenant in var.tenants : tenant => google_secret_manager_secret.mcp_api_keys[tenant].id
  }
}

# Create initial secret versions (empty - to be populated by rotation script)
resource "google_secret_manager_secret_version" "mcp_initial_versions" {
  for_each = toset(var.tenants)
  
  secret      = google_secret_manager_secret.mcp_api_keys[each.value].id
  secret_data = "PLACEHOLDER_KEY_TO_BE_ROTATED"
  
  lifecycle {
    ignore_changes = [secret_data]
  }
}
