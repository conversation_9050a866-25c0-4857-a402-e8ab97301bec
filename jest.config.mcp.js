/**
 * Jest configuration for MCP Rules Engine tests
 */

module.exports = {
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  testMatch: ['**/tests/mcp.e2e.spec.ts'],
  collectCoverageFrom: [
    'packages/mcp-client/src/**/*.ts',
    'apps/backend/src/middleware/mcpMetrics.ts',
  ],
  coverageDirectory: 'coverage/mcp',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
    }],
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.mcp.js'],
};
