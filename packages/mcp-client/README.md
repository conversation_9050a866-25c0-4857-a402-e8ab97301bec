# @ailex/mcp-client

TypeScript client for the MCP Rules Engine API that provides deadline calculation and health check functionality with built-in retry logic and error handling.

## Features

- 🔄 **Automatic Retry Logic**: Exponential backoff with configurable retry attempts
- 🛡️ **Error Handling**: Comprehensive error types with detailed information
- 📊 **TypeScript Support**: Full type safety with detailed interfaces
- 🔐 **Secure**: API key authentication with proper header management
- ⚡ **Performance**: Configurable timeouts and connection pooling
- 📝 **Logging**: Built-in request/response logging capabilities

## Installation

```bash
npm install @ailex/mcp-client
```

## Quick Start

```typescript
import { McpClient } from '@ailex/mcp-client';

const client = new McpClient({
  baseUrl: 'https://rules.ailexlaw.com',
  apiKey: 'your-api-key',
  timeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
});

// Calculate deadlines
const deadlines = await client.calculateDeadlines(
  'TX',           // jurisdiction
  'ACCIDENT',     // trigger code
  '2023-01-15',   // start date
  'personal-injury' // practice area (optional)
);

// Health check
const health = await client.healthCheck();
```

## API Reference

### McpClient

#### Constructor

```typescript
new McpClient(config: McpClientConfig)
```

**Parameters:**
- `config.baseUrl` (string): Base URL of the MCP Rules Engine API
- `config.apiKey` (string): API key for authentication
- `config.timeout` (number, optional): Request timeout in milliseconds (default: 30000)
- `config.maxRetries` (number, optional): Maximum number of retry attempts (default: 3)
- `config.retryDelay` (number, optional): Base delay between retries in milliseconds (default: 1000)

#### Methods

##### calculateDeadlines()

```typescript
calculateDeadlines(
  jurisdiction: string,
  triggerCode: string,
  startDate: string,
  practiceArea?: string
): Promise<DeadlineResponse>
```

Calculate legal deadlines for a given jurisdiction and trigger event.

**Parameters:**
- `jurisdiction`: Legal jurisdiction code (e.g., 'TX', 'CA')
- `triggerCode`: Event trigger code (e.g., 'ACCIDENT', 'FILING')
- `startDate`: Start date in ISO 8601 format
- `practiceArea`: Optional practice area filter

**Returns:** Promise resolving to `DeadlineResponse`

##### healthCheck()

```typescript
healthCheck(): Promise<HealthCheckResponse>
```

Perform a health check on the MCP Rules Engine.

**Returns:** Promise resolving to `HealthCheckResponse`

## Types

### DeadlineResponse

```typescript
interface DeadlineResponse {
  deadlines: Deadline[];
  jurisdiction: string;
  triggerCode: string;
  startDate: string;
  practiceArea?: string;
}
```

### Deadline

```typescript
interface Deadline {
  id: string;
  name: string;
  description: string;
  dueDate: string; // ISO 8601 date string
  priority: 'high' | 'medium' | 'low';
  category: string;
  isStatutory: boolean;
  source?: string;
  notes?: string;
}
```

### HealthCheckResponse

```typescript
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version?: string;
  uptime?: number;
}
```

### McpApiError

```typescript
class McpApiError extends Error {
  status: number;
  code?: string;
  details?: any;
}
```

## Error Handling

The client automatically handles various error scenarios:

- **4xx Client Errors**: No retry, immediate failure
- **429 Rate Limiting**: Automatic retry with exponential backoff
- **5xx Server Errors**: Automatic retry with exponential backoff
- **Network Errors**: Automatic retry with exponential backoff

```typescript
import { McpApiError } from '@ailex/mcp-client';

try {
  const deadlines = await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15');
} catch (error) {
  if (error instanceof McpApiError) {
    console.error(`API Error ${error.status}: ${error.message}`);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.details) {
      console.error('Error details:', error.details);
    }
  } else {
    console.error('Network or other error:', error);
  }
}
```

## Retry Logic

The client implements exponential backoff with the following behavior:

1. **Initial Request**: First attempt
2. **Retry 1**: Wait `retryDelay` ms (default: 1000ms)
3. **Retry 2**: Wait `retryDelay * 2` ms (default: 2000ms)
4. **Retry 3**: Wait `retryDelay * 4` ms (default: 4000ms)

Retries are triggered for:
- HTTP 5xx server errors
- HTTP 429 rate limiting
- Network timeouts and connection errors

Retries are **not** triggered for:
- HTTP 4xx client errors (except 429)
- Authentication failures
- Malformed requests

## Configuration Examples

### Production Configuration

```typescript
const client = new McpClient({
  baseUrl: process.env.MCP_RULES_BASE!,
  apiKey: process.env.MCP_API_KEY!,
  timeout: 30000,    // 30 seconds
  maxRetries: 3,     // 3 retry attempts
  retryDelay: 2000,  // 2 second base delay
});
```

### Development Configuration

```typescript
const client = new McpClient({
  baseUrl: 'https://rules-dev.ailexlaw.com',
  apiKey: 'dev-api-key',
  timeout: 10000,    // 10 seconds
  maxRetries: 1,     // 1 retry attempt
  retryDelay: 500,   // 500ms base delay
});
```

### High-Volume Configuration

```typescript
const client = new McpClient({
  baseUrl: process.env.MCP_RULES_BASE!,
  apiKey: process.env.MCP_API_KEY!,
  timeout: 60000,    // 60 seconds
  maxRetries: 5,     // 5 retry attempts
  retryDelay: 1000,  // 1 second base delay
});
```

## Testing

The package includes comprehensive tests with mocked fetch responses:

```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test -- --testNamePattern="calculateDeadlines"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details.
