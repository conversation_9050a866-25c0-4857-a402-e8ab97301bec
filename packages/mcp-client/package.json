{"name": "@ailex/mcp-client", "version": "1.0.0", "description": "TypeScript client for MCP Rules Engine API", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["mcp", "rules-engine", "legal", "deadlines", "typescript"], "author": "AiLex Team", "license": "MIT", "dependencies": {"node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*"]}