{"name": "@ailex/mcp-client", "version": "1.0.0", "description": "TypeScript client for MCP Rules Engine API", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "test:unit": "jest src/__tests__/unit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:watch": "jest --coverage --watch", "test:ci": "jest --coverage --ci --watchAll=false", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf dist coverage", "prepublishOnly": "npm run clean && npm run build && npm run test:ci"}, "keywords": ["mcp", "rules-engine", "legal", "deadlines", "typescript"], "author": "AiLex Team", "license": "MIT", "dependencies": {"node-fetch": "^3.3.2"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.4.0", "typescript": "^5.0.0"}, "files": ["dist/**/*"]}