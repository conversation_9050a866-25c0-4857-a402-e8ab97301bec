/**
 * MCP Rules Engine Client
 *
 * TypeScript wrapper for the MCP Rules Engine API that provides
 * deadline calculation and health check functionality.
 */

import fetch, { RequestInit, Response } from 'node-fetch';
import {
  DeadlineRequest,
  DeadlineResponse,
  HealthCheckResponse,
  McpClientConfig,
  McpApiError,
} from './types.js';

export * from './types.js';

export class McpClient {
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly timeout: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;

  // Circuit breaker state
  private circuitBreakerState: 'closed' | 'open' | 'half-open' = 'closed';
  private consecutiveFailures = 0;
  private lastFailureTime = 0;
  private readonly failureThreshold = 3;
  private readonly recoveryTimeout = 30000; // 30 seconds

  constructor(config: McpClientConfig) {
    this.baseUrl = config.baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000; // 30 seconds default
    this.maxRetries = config.maxRetries || 3;
    this.retryDelay = config.retryDelay || 1000; // 1 second base delay
  }

  /**
   * Calculate deadlines for a given jurisdiction and trigger code
   */
  async calculateDeadlines(
    jurisdiction: string,
    triggerCode: string,
    startDate: string,
    practiceArea?: string
  ): Promise<DeadlineResponse> {
    const request: DeadlineRequest = {
      jurisdiction,
      triggerCode,
      startDate,
      practiceArea,
    };

    return this.makeRequest<DeadlineResponse>('/api/v1/deadlines/calculate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Perform health check on the MCP Rules Engine
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    return this.makeRequest<HealthCheckResponse>('/health');
  }

  /**
   * Make an HTTP request with retry logic, error handling, and circuit breaker
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Check circuit breaker state
    if (this.circuitBreakerState === 'open') {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure < this.recoveryTimeout) {
        throw new McpApiError(
          'Circuit breaker is OPEN - service temporarily unavailable',
          503,
          'CIRCUIT_BREAKER_OPEN'
        );
      } else {
        // Transition to half-open for testing
        this.circuitBreakerState = 'half-open';
      }
    }

    const url = `${this.baseUrl}${endpoint}`;

    const headers = {
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey,
      ...options.headers,
    };

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await fetch(url, {
          ...requestOptions,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorBody = await this.safeParseJson(response);
          throw new McpApiError(
            errorBody?.message || `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            errorBody?.code,
            errorBody
          );
        }

        const data = await response.json();

        // Success - reset circuit breaker
        this.onSuccess();

        return data as T;
      } catch (error) {
        lastError = error as Error;

        // Record failure for circuit breaker
        this.onFailure();

        // Don't retry on client errors (4xx) except for rate limiting (429)
        if (error instanceof McpApiError &&
            error.status >= 400 &&
            error.status < 500 &&
            error.status !== 429) {
          throw error;
        }

        // If this is the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw error;
        }

        // Wait before retrying with exponential backoff
        const delay = this.retryDelay * Math.pow(2, attempt);
        await this.sleep(delay);
      }
    }

    // This should never be reached, but just in case
    throw lastError || new Error('Unknown error occurred');
  }

  /**
   * Safely parse JSON response, returning null if parsing fails
   */
  private async safeParseJson(response: Response): Promise<any> {
    try {
      return await response.json();
    } catch {
      return null;
    }
  }

  /**
   * Sleep for the specified number of milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Handle successful request - reset circuit breaker
   */
  private onSuccess(): void {
    this.consecutiveFailures = 0;
    this.circuitBreakerState = 'closed';
  }

  /**
   * Handle failed request - update circuit breaker state
   */
  private onFailure(): void {
    this.consecutiveFailures++;
    this.lastFailureTime = Date.now();

    if (this.consecutiveFailures >= this.failureThreshold) {
      this.circuitBreakerState = 'open';
      console.warn(
        `MCP Circuit breaker OPENED after ${this.consecutiveFailures} consecutive failures. ` +
        `Will retry after ${this.recoveryTimeout}ms`
      );
    }
  }

  /**
   * Get current circuit breaker state for monitoring
   */
  public getCircuitBreakerState(): {
    state: 'closed' | 'open' | 'half-open';
    consecutiveFailures: number;
    lastFailureTime: number;
  } {
    return {
      state: this.circuitBreakerState,
      consecutiveFailures: this.consecutiveFailures,
      lastFailureTime: this.lastFailureTime,
    };
  }
}
