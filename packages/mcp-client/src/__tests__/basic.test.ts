/**
 * Basic tests for MCP Client to verify setup
 */

import { McpClient, McpApiError } from '../index';

describe('MCP Client Basic Tests', () => {
  describe('Initialization', () => {
    it('should initialize with required configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      expect(client).toBeDefined();
      expect(client).toBeInstanceOf(McpClient);
    });

    it('should initialize circuit breaker in closed state', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
      expect(state.lastFailureTime).toBe(0);
    });

    it('should strip trailing slash from baseUrl', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com/',
        apiKey: 'test-api-key'
      });

      expect(client).toBeDefined();
    });

    it('should apply custom configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        timeout: 5000,
        maxRetries: 1,
        retryDelay: 500
      });

      expect(client).toBeDefined();
    });
  });

  describe('Error Classes', () => {
    it('should create McpApiError with all properties', () => {
      const error = new McpApiError(
        'Test error message',
        400,
        'TEST_ERROR',
        { field: 'test' }
      );

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(McpApiError);
      expect(error.message).toBe('Test error message');
      expect(error.status).toBe(400);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.details).toEqual({ field: 'test' });
    });

    it('should create McpApiError with minimal properties', () => {
      const error = new McpApiError('Simple error', 500);

      expect(error.message).toBe('Simple error');
      expect(error.status).toBe(500);
      expect(error.code).toBeUndefined();
      expect(error.details).toBeUndefined();
    });
  });

  describe('Circuit Breaker State', () => {
    it('should return circuit breaker state object', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      const state = client.getCircuitBreakerState();

      expect(state).toHaveProperty('state');
      expect(state).toHaveProperty('consecutiveFailures');
      expect(state).toHaveProperty('lastFailureTime');
      expect(typeof state.state).toBe('string');
      expect(typeof state.consecutiveFailures).toBe('number');
      expect(typeof state.lastFailureTime).toBe('number');
    });

    it('should have valid circuit breaker states', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      const state = client.getCircuitBreakerState();
      const validStates = ['closed', 'open', 'half-open'];
      
      expect(validStates).toContain(state.state);
    });
  });
});
