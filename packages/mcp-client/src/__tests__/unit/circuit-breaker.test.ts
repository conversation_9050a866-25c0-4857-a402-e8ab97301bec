/**
 * Unit tests for Circuit Breaker functionality
 */

import { McpClient, McpApiError } from '../../index';

// Mock node-fetch
jest.mock('node-fetch', () => jest.fn());

const mockFetch = require('node-fetch') as jest.MockedFunction<typeof fetch>;

describe('Circuit Breaker', () => {
  let client: McpClient;
  let originalDateNow: typeof Date.now;
  let currentTime: number;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Date.now for time-based testing
    originalDateNow = Date.now;
    currentTime = 1000000; // Start at a fixed time
    Date.now = jest.fn(() => currentTime);

    client = new McpClient({
      baseUrl: 'https://rules.ailexlaw.com',
      apiKey: 'test-api-key',
      maxRetries: 0, // Disable retries for circuit breaker testing
      retryDelay: 10
    });
  });

  afterEach(() => {
    Date.now = originalDateNow;
  });

  describe('Initial State', () => {
    it('should start in closed state', () => {
      const state = client.getCircuitBreakerState();
      
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
      expect(state.lastFailureTime).toBe(0);
    });
  });

  describe('Failure Tracking', () => {
    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });
    });

    it('should track consecutive failures', async () => {
      // First failure
      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
      } catch (error) {
        // Expected to fail
      }

      let state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(1);
      expect(state.lastFailureTime).toBe(currentTime);

      // Advance time for second failure
      currentTime += 1000;

      // Second failure
      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
      } catch (error) {
        // Expected to fail
      }

      state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(2);
      expect(state.lastFailureTime).toBe(currentTime);
    });

    it('should open circuit breaker after 3 consecutive failures', async () => {
      // Make 3 consecutive failures
      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('open');
      expect(state.consecutiveFailures).toBe(3);
    });

    it('should reset failure count on successful request', async () => {
      // First, make 2 failures
      for (let i = 0; i < 2; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      expect(client.getCircuitBreakerState().consecutiveFailures).toBe(2);

      // Now make a successful request
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ deadlines: [] })
      });

      await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
    });
  });

  describe('Open State Behavior', () => {
    beforeEach(async () => {
      // Open the circuit breaker by making 3 failures
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });

      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      expect(client.getCircuitBreakerState().state).toBe('open');
    });

    it('should fail immediately when circuit is open', async () => {
      const startTime = currentTime;
      
      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected circuit breaker error');
      } catch (error) {
        expect(error).toBeInstanceOf(McpApiError);
        expect((error as McpApiError).code).toBe('CIRCUIT_BREAKER_OPEN');
        expect((error as McpApiError).message).toContain('Circuit breaker is OPEN');
        expect((error as McpApiError).status).toBe(503);
      }

      // Should not have made any additional HTTP requests
      expect(mockFetch).toHaveBeenCalledTimes(3); // Only the initial 3 failures
    });

    it('should remain open during recovery timeout', async () => {
      // Advance time by 29 seconds (less than 30 second timeout)
      currentTime += 29000;

      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected circuit breaker error');
      } catch (error) {
        expect((error as McpApiError).code).toBe('CIRCUIT_BREAKER_OPEN');
      }

      expect(client.getCircuitBreakerState().state).toBe('open');
    });
  });

  describe('Half-Open State and Recovery', () => {
    beforeEach(async () => {
      // Open the circuit breaker
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });

      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      expect(client.getCircuitBreakerState().state).toBe('open');
    });

    it('should transition to half-open after recovery timeout', async () => {
      // Advance time by 30 seconds (recovery timeout)
      currentTime += 30000;

      // Mock a successful response for recovery
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({ deadlines: [] })
      });

      await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
    });

    it('should reopen if half-open request fails', async () => {
      // Advance time by 30 seconds
      currentTime += 30000;

      // Mock a failed response during half-open state
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });

      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
      } catch (error) {
        // Expected to fail
      }

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('open');
      expect(state.consecutiveFailures).toBe(4); // 3 initial + 1 half-open failure
    });
  });

  describe('Circuit Breaker with Different Error Types', () => {
    it('should not count 4xx errors (except 429) as circuit breaker failures', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({ message: 'Invalid request' })
      });

      // Make 5 requests with 400 errors
      for (let i = 0; i < 5; i++) {
        try {
          await client.calculateDeadlines('INVALID', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should still be closed since 4xx errors don't count
      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
    });

    it('should count 429 rate limiting as circuit breaker failures', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        json: jest.fn().mockResolvedValue({ message: 'Rate limited' })
      });

      // Make 3 requests with 429 errors
      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should be open
      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('open');
      expect(state.consecutiveFailures).toBe(3);
    });

    it('should count network errors as circuit breaker failures', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Make 3 requests with network errors
      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should be open
      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('open');
      expect(state.consecutiveFailures).toBe(3);
    });
  });

  describe('Health Check Circuit Breaker', () => {
    it('should apply circuit breaker to health check requests', async () => {
      // Open circuit breaker with calculateDeadlines failures
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });

      for (let i = 0; i < 3; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      expect(client.getCircuitBreakerState().state).toBe('open');

      // Health check should also fail with circuit breaker error
      try {
        await client.healthCheck();
        fail('Expected circuit breaker error');
      } catch (error) {
        expect((error as McpApiError).code).toBe('CIRCUIT_BREAKER_OPEN');
      }
    });
  });

  describe('Concurrent Requests', () => {
    it('should handle concurrent requests during state transitions', async () => {
      // Set up to open circuit breaker
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: jest.fn().mockResolvedValue({ message: 'Server error' })
      });

      // Make 2 failures first
      for (let i = 0; i < 2; i++) {
        currentTime += 1000;
        try {
          await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        } catch (error) {
          // Expected to fail
        }
      }

      // Now make multiple concurrent requests that should trigger circuit breaker opening
      currentTime += 1000;
      const promises = Array.from({ length: 5 }, () =>
        client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z').catch(e => e)
      );

      const results = await Promise.all(promises);

      // Circuit breaker should be open
      expect(client.getCircuitBreakerState().state).toBe('open');

      // Some requests might have completed before circuit opened, others should get circuit breaker error
      const circuitBreakerErrors = results.filter(r => r.code === 'CIRCUIT_BREAKER_OPEN');
      const serverErrors = results.filter(r => r.status === 500);

      expect(circuitBreakerErrors.length + serverErrors.length).toBe(5);
    });
  });
});
