/**
 * MCP Rules Engine Client
 *
 * TypeScript wrapper for the MCP Rules Engine API that provides
 * deadline calculation and health check functionality.
 */
import { DeadlineResponse, HealthCheckResponse, McpClientConfig } from './types.js';
export * from './types.js';
export declare class McpClient {
    private readonly baseUrl;
    private readonly apiKey;
    private readonly timeout;
    private readonly maxRetries;
    private readonly retryDelay;
    constructor(config: McpClientConfig);
    /**
     * Calculate deadlines for a given jurisdiction and trigger code
     */
    calculateDeadlines(jurisdiction: string, triggerCode: string, startDate: string, practiceArea?: string): Promise<DeadlineResponse>;
    /**
     * Perform health check on the MCP Rules Engine
     */
    healthCheck(): Promise<HealthCheckResponse>;
    /**
     * Make an HTTP request with retry logic and error handling
     */
    private makeRequest;
    /**
     * Safely parse JSON response, returning null if parsing fails
     */
    private safeParseJson;
    /**
     * Sleep for the specified number of milliseconds
     */
    private sleep;
}
//# sourceMappingURL=index.d.ts.map