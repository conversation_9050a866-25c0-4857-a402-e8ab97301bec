/**
 * MCP Rules Engine Client
 *
 * TypeScript wrapper for the MCP Rules Engine API that provides
 * deadline calculation and health check functionality.
 */
import fetch from 'node-fetch';
import { McpApiError, } from './types.js';
export * from './types.js';
export class McpClient {
    constructor(config) {
        this.baseUrl = config.baseUrl.replace(/\/$/, ''); // Remove trailing slash
        this.apiKey = config.apiKey;
        this.timeout = config.timeout || 30000; // 30 seconds default
        this.maxRetries = config.maxRetries || 3;
        this.retryDelay = config.retryDelay || 1000; // 1 second base delay
    }
    /**
     * Calculate deadlines for a given jurisdiction and trigger code
     */
    async calculateDeadlines(jurisdiction, triggerCode, startDate, practiceArea) {
        const request = {
            jurisdiction,
            triggerCode,
            startDate,
            practiceArea,
        };
        return this.makeRequest('/api/v1/deadlines/calculate', {
            method: 'POST',
            body: JSON.stringify(request),
        });
    }
    /**
     * Perform health check on the MCP Rules Engine
     */
    async healthCheck() {
        return this.makeRequest('/health');
    }
    /**
     * Make an HTTP request with retry logic and error handling
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            'x-api-key': this.apiKey,
            ...options.headers,
        };
        const requestOptions = {
            ...options,
            headers,
        };
        let lastError = null;
        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                // Create AbortController for timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                const response = await fetch(url, {
                    ...requestOptions,
                    signal: controller.signal,
                });
                clearTimeout(timeoutId);
                if (!response.ok) {
                    const errorBody = await this.safeParseJson(response);
                    throw new McpApiError(errorBody?.message || `HTTP ${response.status}: ${response.statusText}`, response.status, errorBody?.code, errorBody);
                }
                const data = await response.json();
                return data;
            }
            catch (error) {
                lastError = error;
                // Don't retry on client errors (4xx) except for rate limiting (429)
                if (error instanceof McpApiError &&
                    error.status >= 400 &&
                    error.status < 500 &&
                    error.status !== 429) {
                    throw error;
                }
                // If this is the last attempt, throw the error
                if (attempt === this.maxRetries) {
                    throw error;
                }
                // Wait before retrying with exponential backoff
                const delay = this.retryDelay * Math.pow(2, attempt);
                await this.sleep(delay);
            }
        }
        // This should never be reached, but just in case
        throw lastError || new Error('Unknown error occurred');
    }
    /**
     * Safely parse JSON response, returning null if parsing fails
     */
    async safeParseJson(response) {
        try {
            return await response.json();
        }
        catch {
            return null;
        }
    }
    /**
     * Sleep for the specified number of milliseconds
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
//# sourceMappingURL=index.js.map