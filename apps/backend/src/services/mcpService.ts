/**
 * MCP Rules Engine Service Integration
 * 
 * Service layer for integrating MCP Rules Engine into case creation flow
 * with feature flag support and error handling.
 */

import { McpClient, McpApiError, DeadlineResponse } from '@ailex/mcp-client';
import { logMcpOperation } from '../middleware/mcpMetrics.js';

interface CaseDeadlineRequest {
  tenantId: string;
  jurisdiction: string;
  incidentDate: string;
  caseType?: string;
  practiceArea?: string;
}

interface ProcessedDeadline {
  id: string;
  name: string;
  description: string;
  dueDate: Date;
  priority: 'high' | 'medium' | 'low';
  category: string;
  isStatutory: boolean;
  daysFromIncident: number;
  source?: string;
  notes?: string;
}

export class McpService {
  private client: McpClient;
  private isEnabled: boolean;

  constructor() {
    this.client = new McpClient({
      baseUrl: process.env.MCP_RULES_BASE || 'https://rules.ailexlaw.com',
      apiKey: process.env.MCP_API_KEY || '',
      timeout: 30000,
      maxRetries: 3,
      retryDelay: 1000,
    });
    
    // Feature flag for MCP Rules Engine
    this.isEnabled = process.env.FEATURE_MCP_RULES_ENGINE === 'true';
  }

  /**
   * Calculate deadlines for a new case
   */
  async calculateCaseDeadlines(request: CaseDeadlineRequest): Promise<ProcessedDeadline[]> {
    if (!this.isEnabled) {
      console.log('MCP Rules Engine is disabled via feature flag');
      return [];
    }

    const startTime = Date.now();
    let success = false;
    let errorMessage: string | undefined;

    try {
      // Determine trigger code based on case type
      const triggerCode = this.mapCaseTypeToTriggerCode(request.caseType);
      
      console.log(`Calculating deadlines for tenant ${request.tenantId}`, {
        jurisdiction: request.jurisdiction,
        triggerCode,
        incidentDate: request.incidentDate,
        practiceArea: request.practiceArea,
      });

      const response = await this.client.calculateDeadlines(
        request.jurisdiction,
        triggerCode,
        request.incidentDate,
        request.practiceArea
      );

      const processedDeadlines = this.processDeadlineResponse(response, request.incidentDate);
      success = true;

      console.log(`Successfully calculated ${processedDeadlines.length} deadlines for tenant ${request.tenantId}`);
      
      return processedDeadlines;

    } catch (error) {
      success = false;
      
      if (error instanceof McpApiError) {
        errorMessage = `MCP API Error ${error.status}: ${error.message}`;
        console.error(errorMessage, {
          tenantId: request.tenantId,
          status: error.status,
          code: error.code,
          details: error.details,
        });
      } else {
        errorMessage = `Network or system error: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage, {
          tenantId: request.tenantId,
          error: error instanceof Error ? error.stack : error,
        });
      }

      // For non-critical errors, return empty array instead of throwing
      if (error instanceof McpApiError && error.status >= 400 && error.status < 500) {
        console.warn(`Non-critical MCP error for tenant ${request.tenantId}, continuing without deadlines`);
        return [];
      }

      // For server errors, we might want to throw to trigger retry at a higher level
      throw error;

    } finally {
      const latencyMs = Date.now() - startTime;
      
      // Log metrics
      logMcpOperation(
        request.tenantId,
        'calculate_case_deadlines',
        success,
        latencyMs,
        {
          jurisdiction: request.jurisdiction,
          triggerCode: this.mapCaseTypeToTriggerCode(request.caseType),
          errorMessage,
        }
      );
    }
  }

  /**
   * Check if MCP Rules Engine is healthy
   */
  async checkHealth(): Promise<boolean> {
    if (!this.isEnabled) {
      return false;
    }

    try {
      const health = await this.client.healthCheck();
      return health.status === 'healthy';
    } catch (error) {
      console.error('MCP health check failed:', error);
      return false;
    }
  }

  /**
   * Map case type to MCP trigger code
   */
  private mapCaseTypeToTriggerCode(caseType?: string): string {
    const mapping: Record<string, string> = {
      'motor-vehicle-accident': 'ACCIDENT',
      'slip-and-fall': 'PREMISES_LIABILITY',
      'medical-malpractice': 'MEDICAL_MALPRACTICE',
      'product-liability': 'PRODUCT_LIABILITY',
      'wrongful-death': 'WRONGFUL_DEATH',
      'workers-compensation': 'WORKERS_COMP',
    };

    return mapping[caseType || ''] || 'GENERAL_INJURY';
  }

  /**
   * Process raw deadline response into structured format
   */
  private processDeadlineResponse(
    response: DeadlineResponse,
    incidentDate: string
  ): ProcessedDeadline[] {
    const incidentDateTime = new Date(incidentDate);

    return response.deadlines.map(deadline => {
      const dueDate = new Date(deadline.dueDate);
      const daysFromIncident = Math.ceil(
        (dueDate.getTime() - incidentDateTime.getTime()) / (1000 * 60 * 60 * 24)
      );

      return {
        id: deadline.id,
        name: deadline.name,
        description: deadline.description,
        dueDate,
        priority: deadline.priority,
        category: deadline.category,
        isStatutory: deadline.isStatutory,
        daysFromIncident,
        source: deadline.source,
        notes: deadline.notes,
      };
    });
  }

  /**
   * Get feature flag status
   */
  isFeatureEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Enable or disable the feature (for testing)
   */
  setFeatureEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Export singleton instance
export const mcpService = new McpService();
