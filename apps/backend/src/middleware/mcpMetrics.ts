/**
 * MCP Rules Engine Metrics Middleware
 * 
 * Logs MCP API calls with success/failure status and latency to Google Cloud Logging
 * for monitoring and analytics purposes.
 */

import { Request, Response, NextFunction } from 'express';

interface McpMetricsData {
  tenantId: string;
  operation: string;
  success: boolean;
  latencyMs: number;
  statusCode?: number;
  errorMessage?: string;
  jurisdiction?: string;
  triggerCode?: string;
}

interface CloudLoggingEntry {
  severity: 'INFO' | 'WARNING' | 'ERROR';
  message: string;
  resource: {
    type: string;
    labels: {
      project_id?: string;
      instance_id?: string;
    };
  };
  labels: {
    tenant_id: string;
    service: string;
    operation: string;
    [key: string]: string;
  };
  jsonPayload: {
    success: boolean;
    latency_ms: number;
    status_code?: number;
    error_message?: string;
    jurisdiction?: string;
    trigger_code?: string;
    timestamp: string;
  };
}

/**
 * Middleware to track MCP API call metrics
 */
export function mcpMetricsMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Only apply to MCP-related routes
    if (!req.path.includes('/mcp') && !req.path.includes('/deadlines')) {
      return next();
    }

    const startTime = Date.now();
    
    // Store original end function
    const originalEnd = res.end;
    
    // Override res.end to capture metrics
    res.end = function(chunk?: any, encoding?: any) {
      const endTime = Date.now();
      const latencyMs = endTime - startTime;
      
      // Extract tenant ID from request (assuming it's in JWT or headers)
      const tenantId = extractTenantId(req);
      
      // Determine operation type
      const operation = determineOperation(req);
      
      // Create metrics data
      const metricsData: McpMetricsData = {
        tenantId,
        operation,
        success: res.statusCode >= 200 && res.statusCode < 400,
        latencyMs,
        statusCode: res.statusCode,
        jurisdiction: req.body?.jurisdiction,
        triggerCode: req.body?.triggerCode,
      };

      // Add error message if request failed
      if (!metricsData.success) {
        metricsData.errorMessage = getErrorMessage(res, chunk);
      }

      // Log metrics to Cloud Logging
      logMcpMetrics(metricsData);
      
      // Call original end function
      originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

/**
 * Extract tenant ID from request
 */
function extractTenantId(req: Request): string {
  // Try to get tenant ID from JWT token
  if (req.user && (req.user as any).tenant_id) {
    return (req.user as any).tenant_id;
  }
  
  // Try to get from headers
  if (req.headers['x-tenant-id']) {
    return req.headers['x-tenant-id'] as string;
  }
  
  // Try to get from query params
  if (req.query.tenant_id) {
    return req.query.tenant_id as string;
  }
  
  // Default to unknown
  return 'unknown';
}

/**
 * Determine the operation type from the request
 */
function determineOperation(req: Request): string {
  if (req.path.includes('/health')) {
    return 'health_check';
  }
  
  if (req.path.includes('/deadlines/calculate')) {
    return 'calculate_deadlines';
  }
  
  if (req.path.includes('/mcp')) {
    return 'mcp_api_call';
  }
  
  return 'unknown_operation';
}

/**
 * Extract error message from response
 */
function getErrorMessage(res: Response, chunk?: any): string {
  try {
    if (chunk && typeof chunk === 'string') {
      const parsed = JSON.parse(chunk);
      return parsed.message || parsed.error || 'Unknown error';
    }
  } catch {
    // Ignore parsing errors
  }
  
  return `HTTP ${res.statusCode}`;
}

/**
 * Log metrics to Google Cloud Logging
 */
function logMcpMetrics(data: McpMetricsData): void {
  const logEntry: CloudLoggingEntry = {
    severity: data.success ? 'INFO' : 'ERROR',
    message: `MCP ${data.operation} - ${data.success ? 'SUCCESS' : 'FAILURE'}`,
    resource: {
      type: 'generic_node',
      labels: {
        project_id: process.env.GOOGLE_CLOUD_PROJECT,
        instance_id: process.env.GAE_INSTANCE || 'local',
      },
    },
    labels: {
      tenant_id: data.tenantId,
      service: 'mcp-rules-engine',
      operation: data.operation,
      success: data.success.toString(),
    },
    jsonPayload: {
      success: data.success,
      latency_ms: data.latencyMs,
      status_code: data.statusCode,
      error_message: data.errorMessage,
      jurisdiction: data.jurisdiction,
      trigger_code: data.triggerCode,
      timestamp: new Date().toISOString(),
    },
  };

  // In production, this would use the Google Cloud Logging client
  // For now, we'll use structured console logging
  if (process.env.NODE_ENV === 'production') {
    console.log(JSON.stringify(logEntry));
  } else {
    console.log(`[MCP Metrics] ${logEntry.message}`, {
      tenant: data.tenantId,
      operation: data.operation,
      success: data.success,
      latency: `${data.latencyMs}ms`,
      status: data.statusCode,
    });
  }
}

/**
 * Utility function to manually log MCP metrics from within route handlers
 */
export function logMcpOperation(
  tenantId: string,
  operation: string,
  success: boolean,
  latencyMs: number,
  additionalData?: Partial<McpMetricsData>
): void {
  const metricsData: McpMetricsData = {
    tenantId,
    operation,
    success,
    latencyMs,
    ...additionalData,
  };
  
  logMcpMetrics(metricsData);
}
