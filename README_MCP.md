# MCP Rules Engine Integration

This document provides comprehensive setup instructions, environment variable descriptions, sample usage, and FAQs for integrating AiLex backend with the MCP Rules Engine.

## Overview

The MCP Rules Engine integration allows AiLex tenants to receive legal deadlines transparently through a secure API Gateway. Each tenant has their own API key for usage tracking and security isolation.

## Architecture

```
AiLex Backend → MCP Client → API Gateway → MCP Rules Engine
                    ↓
              Cloud Logging (Metrics)
                    ↓
              Secret Manager (API Keys)
```

## Setup Instructions

### 1. Infrastructure Setup

Deploy the Terraform infrastructure to create Secret Manager secrets and API keys:

```bash
cd infra/terraform

# Initialize Terraform
terraform init

# Plan the deployment
terraform plan -var="project_id=your-gcp-project" \
               -var="tenants=[\"pilot-smith\",\"demo-tenant\"]"

# Apply the infrastructure
terraform apply
```

### 2. Environment Variables

Add the following variables to your `.env` file:

```bash
# MCP Rules Engine Configuration
MCP_RULES_BASE=https://rules.ailexlaw.com
MCP_API_KEY=projects/${PROJECT_ID}/secrets/mcp-key-<TENANT>/versions/latest

# Google Cloud Project (for Secret Manager access)
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
```

### 3. Install Dependencies

Install the MCP client package:

```bash
# Install the MCP client
npm install @ailex/mcp-client

# Or if using the local package
cd packages/mcp-client
npm install
npm run build
```

### 4. Backend Integration

Add the MCP metrics middleware to your Express app:

```typescript
import { mcpMetricsMiddleware } from './middleware/mcpMetrics.js';

// Add middleware
app.use(mcpMetricsMiddleware());
```

### 5. Key Rotation Setup

Set up the key rotation script:

```bash
# Make the script executable
chmod +x scripts/rotate-mcp-key.ts

# Install dependencies
npm install commander @google-cloud/secret-manager @google-cloud/apikeys

# Test with dry run
npx ts-node scripts/rotate-mcp-key.ts rotate --tenant pilot-smith --dry-run
```

## Usage Examples

### Basic Usage

```typescript
import { McpClient } from '@ailex/mcp-client';

// Initialize client
const mcpClient = new McpClient({
  baseUrl: process.env.MCP_RULES_BASE!,
  apiKey: process.env.MCP_API_KEY!,
  timeout: 30000,
  maxRetries: 3,
});

// Calculate deadlines
const deadlines = await mcpClient.calculateDeadlines(
  'TX',           // jurisdiction
  'ACCIDENT',     // trigger code
  '2023-01-15',   // start date
  'personal-injury' // practice area (optional)
);

console.log('Deadlines:', deadlines);
```

### Health Check

```typescript
// Check if MCP Rules Engine is healthy
const health = await mcpClient.healthCheck();
console.log('Health status:', health.status);
```

### Error Handling

```typescript
import { McpApiError } from '@ailex/mcp-client';

try {
  const deadlines = await mcpClient.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15');
} catch (error) {
  if (error instanceof McpApiError) {
    console.error(`API Error ${error.status}: ${error.message}`);
    console.error('Error code:', error.code);
    console.error('Details:', error.details);
  } else {
    console.error('Network or other error:', error);
  }
}
```

## Sample curl Commands

### Calculate Deadlines

```bash
curl -X POST https://rules.ailexlaw.com/api/v1/deadlines/calculate \
  -H "Content-Type: application/json" \
  -H "x-api-key: YOUR_API_KEY" \
  -d '{
    "jurisdiction": "TX",
    "triggerCode": "ACCIDENT",
    "startDate": "2023-01-15T00:00:00Z",
    "practiceArea": "personal-injury"
  }'
```

### Health Check

```bash
curl -X GET https://rules.ailexlaw.com/health \
  -H "x-api-key: YOUR_API_KEY"
```

## Key Management

### Rotate API Key

```bash
# Rotate key for a tenant
npx ts-node scripts/rotate-mcp-key.ts rotate --tenant pilot-smith

# Dry run (no actual changes)
npx ts-node scripts/rotate-mcp-key.ts rotate --tenant pilot-smith --dry-run

# Keep old key after rotation
npx ts-node scripts/rotate-mcp-key.ts rotate --tenant pilot-smith --keep-old
```

### List API Keys

```bash
# List all MCP API keys
npx ts-node scripts/rotate-mcp-key.ts list
```

## Monitoring and Metrics

### Cloud Logging

Metrics are automatically logged to Google Cloud Logging with the following structure:

```json
{
  "severity": "INFO",
  "message": "MCP calculate_deadlines - SUCCESS",
  "resource": {
    "type": "generic_node",
    "labels": {
      "project_id": "your-project",
      "instance_id": "your-instance"
    }
  },
  "labels": {
    "tenant_id": "pilot-smith",
    "service": "mcp-rules-engine",
    "operation": "calculate_deadlines",
    "success": "true"
  },
  "jsonPayload": {
    "success": true,
    "latency_ms": 245,
    "status_code": 200,
    "jurisdiction": "TX",
    "trigger_code": "ACCIDENT",
    "timestamp": "2023-01-15T12:00:00Z"
  }
}
```

### Query Metrics

Use Google Cloud Logging to query metrics:

```sql
-- Success rate by tenant
resource.type="generic_node"
labels.service="mcp-rules-engine"
labels.operation="calculate_deadlines"

-- Average latency
resource.type="generic_node"
labels.service="mcp-rules-engine"
jsonPayload.latency_ms>0
```

## Testing

### Run E2E Tests

```bash
# Run all MCP tests
npm test tests/mcp.e2e.spec.ts

# Run with coverage
npm test -- --coverage tests/mcp.e2e.spec.ts
```

### Unit Tests

```bash
# Test the MCP client package
cd packages/mcp-client
npm test
```

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   ```
   Error: Secret not found: projects/PROJECT/secrets/mcp-key-TENANT
   ```
   - Ensure Terraform has been applied
   - Check that the tenant ID matches the secret name

2. **Permission Denied**
   ```
   Error: Permission denied accessing secret
   ```
   - Verify service account has `secretmanager.secretAccessor` role
   - Check IAM bindings in Terraform

3. **Rate Limiting**
   ```
   Error: 429 Too Many Requests
   ```
   - The client automatically retries with exponential backoff
   - Consider implementing request queuing for high-volume tenants

4. **Network Timeouts**
   ```
   Error: Request timeout
   ```
   - Increase timeout in client configuration
   - Check network connectivity to API Gateway

### Debug Mode

Enable debug logging:

```typescript
const mcpClient = new McpClient({
  baseUrl: process.env.MCP_RULES_BASE!,
  apiKey: process.env.MCP_API_KEY!,
  timeout: 30000,
  maxRetries: 3,
});

// Manual logging for debugging
import { logMcpOperation } from './middleware/mcpMetrics.js';

logMcpOperation('tenant-id', 'debug-operation', true, 100, {
  jurisdiction: 'TX',
  triggerCode: 'TEST'
});
```

## Security Considerations

1. **API Key Storage**: API keys are stored in Google Secret Manager with proper IAM controls
2. **Network Security**: All communication uses HTTPS with API Gateway
3. **Tenant Isolation**: Each tenant has a separate API key for tracking and isolation
4. **Key Rotation**: Regular key rotation is supported via the rotation script
5. **Audit Logging**: All API calls are logged for security monitoring

## FAQ

**Q: How often should API keys be rotated?**
A: We recommend rotating API keys every 90 days or immediately if a key is compromised.

**Q: Can I use the same API key for multiple tenants?**
A: No, each tenant should have their own API key for proper usage tracking and security isolation.

**Q: What happens if the MCP Rules Engine is down?**
A: The client will retry with exponential backoff. If all retries fail, an error will be thrown that should be handled gracefully.

**Q: How do I add a new tenant?**
A: Add the tenant ID to the `tenants` variable in Terraform and run `terraform apply`.

**Q: Can I test the integration locally?**
A: Yes, use the dry-run mode for key rotation and mock the API responses in your tests.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Cloud Logging for error details
3. Contact the AiLex development team
4. Create an issue in the project repository
