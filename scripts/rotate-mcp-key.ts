#!/usr/bin/env node

/**
 * MCP API Key Rotation Script
 * 
 * CLI tool to rotate a tenant's MCP Rules Engine API key and update Secret Manager.
 * This script creates a new API key, updates the secret, and optionally deletes the old key.
 */

import { Command } from 'commander';
import { GoogleAuth } from 'google-auth-library';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { APIKeysClient } from '@google-cloud/apikeys';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface RotationOptions {
  tenantId: string;
  projectId: string;
  dryRun: boolean;
  keepOldKey: boolean;
  serviceAccountKeyPath?: string;
}

interface ApiKeyInfo {
  name: string;
  keyString: string;
  displayName: string;
}

class McpKeyRotator {
  private secretClient: SecretManagerServiceClient;
  private apiKeysClient: APIKeysClient;
  private projectId: string;

  constructor(projectId: string, serviceAccountKeyPath?: string) {
    this.projectId = projectId;
    
    const authOptions = serviceAccountKeyPath 
      ? { keyFilename: serviceAccountKeyPath }
      : {};

    this.secretClient = new SecretManagerServiceClient(authOptions);
    this.apiKeysClient = new APIKeysClient(authOptions);
  }

  /**
   * Rotate API key for a specific tenant
   */
  async rotateKey(options: RotationOptions): Promise<void> {
    console.log(`🔄 Starting MCP API key rotation for tenant: ${options.tenantId}`);
    
    if (options.dryRun) {
      console.log('🧪 DRY RUN MODE - No actual changes will be made');
    }

    try {
      // Step 1: Get current API key info
      const currentKeyInfo = await this.getCurrentKeyInfo(options.tenantId);
      console.log(`📋 Current key: ${currentKeyInfo?.displayName || 'Not found'}`);

      // Step 2: Create new API key
      console.log('🔑 Creating new API key...');
      const newKeyInfo = await this.createNewApiKey(options.tenantId, options.dryRun);
      
      if (!options.dryRun && newKeyInfo) {
        console.log(`✅ New API key created: ${newKeyInfo.displayName}`);

        // Step 3: Update Secret Manager
        console.log('🔐 Updating Secret Manager...');
        await this.updateSecret(options.tenantId, newKeyInfo.keyString);
        console.log('✅ Secret Manager updated successfully');

        // Step 4: Optionally delete old key
        if (!options.keepOldKey && currentKeyInfo) {
          console.log('🗑️  Deleting old API key...');
          await this.deleteOldApiKey(currentKeyInfo.name);
          console.log('✅ Old API key deleted');
        } else if (currentKeyInfo) {
          console.log('⚠️  Old API key retained (use --delete-old to remove)');
        }

        console.log('🎉 MCP API key rotation completed successfully!');
      } else if (options.dryRun) {
        console.log('🧪 DRY RUN: Would create new key and update secret');
      }

    } catch (error) {
      console.error('❌ Error during key rotation:', error);
      throw error;
    }
  }

  /**
   * Get current API key information for a tenant
   */
  private async getCurrentKeyInfo(tenantId: string): Promise<ApiKeyInfo | null> {
    try {
      const keyName = `mcp-rules-api-${tenantId}`;
      const [keys] = await this.apiKeysClient.listKeys({
        parent: `projects/${this.projectId}`,
      });

      const currentKey = keys.find(key => 
        key.displayName?.includes(tenantId) || key.name?.includes(keyName)
      );

      if (currentKey && currentKey.name) {
        return {
          name: currentKey.name,
          keyString: '', // We don't get the actual key string from list operation
          displayName: currentKey.displayName || '',
        };
      }

      return null;
    } catch (error) {
      console.warn('⚠️  Could not retrieve current key info:', error);
      return null;
    }
  }

  /**
   * Create a new API key for the tenant
   */
  private async createNewApiKey(tenantId: string, dryRun: boolean): Promise<ApiKeyInfo | null> {
    if (dryRun) {
      return {
        name: `projects/${this.projectId}/locations/global/keys/dry-run-key`,
        keyString: 'dry-run-key-string',
        displayName: `MCP Rules Engine API Key - ${tenantId} (DRY RUN)`,
      };
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const keyRequest = {
      parent: `projects/${this.projectId}`,
      keyId: `mcp-rules-api-${tenantId}-${timestamp}`,
      key: {
        displayName: `MCP Rules Engine API Key - ${tenantId}`,
        restrictions: {
          apiTargets: [{
            service: 'rules-api',
            methods: [
              'GET /health',
              'POST /api/v1/deadlines/calculate'
            ]
          }]
        }
      }
    };

    const [operation] = await this.apiKeysClient.createKey(keyRequest);
    const [key] = await operation.promise();

    if (key && key.name && key.keyString) {
      return {
        name: key.name,
        keyString: key.keyString,
        displayName: key.displayName || '',
      };
    }

    throw new Error('Failed to create new API key');
  }

  /**
   * Update the secret in Secret Manager
   */
  private async updateSecret(tenantId: string, newKeyString: string): Promise<void> {
    const secretName = `projects/${this.projectId}/secrets/mcp-key-${tenantId}`;
    
    await this.secretClient.addSecretVersion({
      parent: secretName,
      payload: {
        data: Buffer.from(newKeyString, 'utf8'),
      },
    });
  }

  /**
   * Delete the old API key
   */
  private async deleteOldApiKey(keyName: string): Promise<void> {
    await this.apiKeysClient.deleteKey({
      name: keyName,
    });
  }

  /**
   * List all MCP API keys for debugging
   */
  async listKeys(): Promise<void> {
    console.log('📋 Listing all MCP API keys...');
    
    const [keys] = await this.apiKeysClient.listKeys({
      parent: `projects/${this.projectId}`,
    });

    const mcpKeys = keys.filter(key => 
      key.displayName?.includes('MCP Rules Engine') || 
      key.name?.includes('mcp-rules-api')
    );

    if (mcpKeys.length === 0) {
      console.log('No MCP API keys found');
      return;
    }

    mcpKeys.forEach(key => {
      console.log(`- ${key.displayName} (${key.name})`);
    });
  }
}

// CLI setup
const program = new Command();

program
  .name('rotate-mcp-key')
  .description('Rotate MCP Rules Engine API keys for tenants')
  .version('1.0.0');

program
  .command('rotate')
  .description('Rotate API key for a specific tenant')
  .requiredOption('-t, --tenant <tenantId>', 'Tenant ID to rotate key for')
  .option('-p, --project <projectId>', 'Google Cloud Project ID', process.env.GOOGLE_CLOUD_PROJECT)
  .option('--dry-run', 'Perform a dry run without making actual changes', false)
  .option('--keep-old', 'Keep the old API key after rotation', false)
  .option('--service-account-key <path>', 'Path to service account key file')
  .action(async (options) => {
    if (!options.project) {
      console.error('❌ Project ID is required. Set GOOGLE_CLOUD_PROJECT or use --project');
      process.exit(1);
    }

    const rotator = new McpKeyRotator(options.project, options.serviceAccountKey);
    
    try {
      await rotator.rotateKey({
        tenantId: options.tenant,
        projectId: options.project,
        dryRun: options.dryRun,
        keepOldKey: options.keepOld,
        serviceAccountKeyPath: options.serviceAccountKey,
      });
    } catch (error) {
      console.error('❌ Rotation failed:', error);
      process.exit(1);
    }
  });

program
  .command('list')
  .description('List all MCP API keys')
  .option('-p, --project <projectId>', 'Google Cloud Project ID', process.env.GOOGLE_CLOUD_PROJECT)
  .option('--service-account-key <path>', 'Path to service account key file')
  .action(async (options) => {
    if (!options.project) {
      console.error('❌ Project ID is required. Set GOOGLE_CLOUD_PROJECT or use --project');
      process.exit(1);
    }

    const rotator = new McpKeyRotator(options.project, options.serviceAccountKey);
    
    try {
      await rotator.listKeys();
    } catch (error) {
      console.error('❌ Failed to list keys:', error);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();
